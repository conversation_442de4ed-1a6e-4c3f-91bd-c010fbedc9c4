{"name": "desktop", "version": "0.1.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "knip": "knip", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^19.2.3", "@angular/common": "^19.2.3", "@angular/compiler": "^19.2.3", "@angular/core": "^19.2.3", "@angular/forms": "^19.2.3", "@angular/platform-browser": "^19.2.3", "@angular/platform-browser-dynamic": "^19.2.3", "@angular/router": "^19.2.3", "@tailwindcss/postcss": "^4.1.10", "mvp.css": "^1.17.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^19.2.4", "@angular/cli": "^19.2.4", "@angular/compiler-cli": "^19.2.3", "@types/jasmine": "~5.1.0", "angular-eslint": "19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.49", "tailwind-scrollbar": "^4.0.0-beta.0", "tailwindcss": "^3.4.17", "typescript": "~5.5.4", "typescript-eslint": "8.18.0"}}