<ul
  class="text-white text-sm h-screen p-3 pl-0 flex flex-col items-start gap-3"
>
  <section class="flex gap-2 w-full">
    <button (click)="addProfile()">Add +</button>
    <div class="grow"></div>
    <button (click)="saveConfigInfo()">
      {{ saveBtnText$ | async }}
    </button>
  </section>
  <ul
    class="w-full grow bg-[var(--color-accent)] overflow-x-hidden overflow-y-auto rounded-md px-3 scrollbar scrollbar-thumb-gray-900 scrollbar-track-[var(--color-accent)]"
  >
    <li
      *ngFor="
        let setting of (appService.configInfo$ | async)?.profiles;
        let idx = index
      "
    >
      <details class="my-3">
        <summary>
          {{ setting.name || "Chưa có tiêu đề" }}
        </summary>
        <ul class="w-full mt-2 shadow-none">
          <div class="flex flex-col gap-1">
            <label>Name</label>
            <input
              type="text"
              [(ngModel)]="setting.name"
              placeholder="Chưa có tiêu đề"
            />
          </div>
          <section class="grid grid-cols-2 gap-6 mt-6">
            <div class="flex flex-col gap-1">
              <label>From path</label>
              <input
                type="text"
                [(ngModel)]="setting.from"
                placeholder="google-drive:/drive"
              />
            </div>
            <div class="flex flex-col gap-1">
              <label>To path</label>
              <input
                type="text"
                [(ngModel)]="setting.to"
                placeholder="./drive"
              />
            </div>
            <div class="flex flex-col gap-1">
              <label>Backup path</label>
              <input
                type="text"
                [(ngModel)]="setting.backup_path"
                placeholder=".backup"
              />
            </div>
            <div class="flex flex-col gap-1">
              <label>Cache path</label>
              <input
                type="text"
                [(ngModel)]="setting.cache_path"
                placeholder=".cache"
              />
            </div>
            <div class="flex flex-col gap-1">
              <label>Parallel</label>
              <input
                type="number"
                [(ngModel)]="setting.parallel"
                placeholder="16"
                min="1"
              />
            </div>
            <div class="flex flex-col gap-1">
              <label>Bandwidth (MB/s)</label>
              <input
                type="number"
                [(ngModel)]="setting.bandwidth"
                placeholder="5"
                min="1"
              />
            </div>
            <ul>
              <li>
                <label>Include paths</label>
              </li>
              <li
                *ngFor="
                  let s of setting.included_paths;
                  trackBy: trackByFn;
                  let idx2 = index
                "
                class="flex gap-2"
              >
                <input
                  type="text"
                  [(ngModel)]="setting.included_paths[idx2]"
                  placeholder="/included/**"
                />
                <button
                  class="border-red-500"
                  (click)="removeIncludePath(idx, idx2)"
                >
                  ✕
                </button>
              </li>
              <li>
                <button (click)="addIncludePath(idx)">+</button>
              </li>
            </ul>
            <ul>
              <li>
                <label>Exclude paths</label>
              </li>
              <li
                *ngFor="
                  let s of setting.excluded_paths;
                  trackBy: trackByFn;
                  let idx2 = index
                "
                class="flex gap-2"
              >
                <input
                  type="text"
                  [(ngModel)]="setting.excluded_paths[idx2]"
                  placeholder="/excluded/**"
                />
                <button
                  class="border-red-500"
                  (click)="removeExcludePath(idx, idx2)"
                >
                  ✕
                </button>
              </li>
              <li>
                <button (click)="addExcludePath(idx)">+</button>
              </li>
            </ul>
          </section>
          <section class="flex justify-start mt-6">
            <button class="delete-btn" (click)="removeProfile(idx)">
              Delete
            </button>
          </section>
        </ul>
      </details>
    </li>
  </ul>
</ul>
