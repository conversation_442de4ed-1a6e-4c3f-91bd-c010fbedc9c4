<div
  class="flex flex-col gap-3 text-sm h-screen p-3 pl-0 overflow-x-hidden overflow-y-auto"
>
  <div class="text-white">
    <div class="w-full flex justify-start align-center gap-2">
      <button
        [ngClass]="{
          underline: appService.currentAction$.value === Action.Pull
        }"
        (click)="
          appService.currentAction$.value !== Action.Pull
            ? pull()
            : appService.stopCommand()
        "
        [disabled]="!(isCurrentProfileValid$ | async)"
      >
        {{ appService.currentAction$.value === Action.Pull ? "Stop" : "Pull" }}
      </button>
      <button
        [ngClass]="{
          underline: appService.currentAction$.value === Action.Push
        }"
        (click)="
          appService.currentAction$.value !== Action.Push
            ? push()
            : appService.stopCommand()
        "
        [disabled]="!(isCurrentProfileValid$ | async)"
      >
        {{ appService.currentAction$.value === Action.Push ? "Stop" : "Push" }}
      </button>
      <button
        [ngClass]="{
          underline: appService.currentAction$.value === Action.Bi
        }"
        (click)="
          appService.currentAction$.value !== Action.Bi ? bi() : stopCommand()
        "
        [disabled]="!(isCurrentProfileValid$ | async)"
      >
        {{ appService.currentAction$.value === Action.Bi ? "Stop" : "Sync" }}
      </button>
      <button
        [ngClass]="{
          underline: appService.currentAction$.value === Action.BiResync
        }"
        (click)="
          appService.currentAction$.value !== Action.BiResync
            ? biResync()
            : stopCommand()
        "
        [disabled]="!(isCurrentProfileValid$ | async)"
      >
        {{
          appService.currentAction$.value === Action.BiResync
            ? "Stop"
            : "Resync"
        }}
      </button>
      <select
        (change)="changeProfile($event)"
      >
        <option selected>Profile is not selected</option>
        <option
          *ngFor="
            let profile of appService.configInfo$.value.profiles;
            let idx = index
          "
          [value]="idx"
          [selected]="(appService.configInfo$| async)?.selected_profile_index === idx"
        >
          {{ profile.name }}
        </option>
      </select>
    </div>
  </div>
  <code class="text-white overflow-hidden">
    <pre>
Working directory: {{ (appService.configInfo$ | async)?.working_dir }}</pre
    >
  </code>
  <code class="text-white grow overflow-hidden">
    <pre>{{ (appService.data$ | async)?.join("\n") }}</pre>
  </code>
</div>
