<div
  class="fixed top-0 left-0 w-screen min-h-screen overflow-hidden grid bg-gray-900"
  style="grid-template-columns: max-content 1fr"
>
  <div class="p-3 text-white">
    <div class="w-full flex justify-center flex-col gap-3">
      <h1 class="block text-center font-bold text-3xl select-none cursor-default">
        Drive
      </h1>
      <button (click)="openHome()" [ngClass]="(tab$ | async) === 'home' ? 'underline' : ''">Home</button>
      <button (click)="openProfiles()" [ngClass]="(tab$ | async) === 'profiles' ? 'underline' : ''">Profiles</button>
      <button (click)="openRemotes()" [ngClass]="(tab$ | async) === 'remotes' ? 'underline' : ''">Remotes</button>
    </div>
  </div>
  <div [ngSwitch]="tab$ | async">
    <app-home *ngSwitchCase="'home'"></app-home>
    <app-profiles *ngSwitchCase="'profiles'"></app-profiles>
    <app-remotes *ngSwitchCase="'remotes'"></app-remotes>
  </div>
</div>
